import os
from flask import Flask, render_template, session, redirect, url_for
from authlib.integrations.flask_client import OAuth
from config import Config
from models import db
from controllers.auth_controller import auth_bp
from controllers.modal_controller import modal_bp
from controllers.airtightness_controller import airtightness_bp
from controllers.sound_insulation_controller import sound_insulation_bp
from controllers.sound_absorption_controller import sound_absorption_bp
from controllers.sound_transmission_controller import sound_transmission_bp
from controllers.wall_mounted_transmission_controller import wall_mounted_transmission_bp
from controllers.material_porosity_flow_resistance_controller import material_porosity_flow_resistance_bp
from decorators import login_required

# 创建Flask应用
app = Flask(__name__)
app.config.from_object(Config)

# 初始化数据库
db.init_app(app)

# 初始化OAuth
oauth = OAuth(app)

# 注册Keycloak客户端
keycloak = oauth.register(
    name='keycloak',
    client_id=app.config['KEYCLOAK_FRONTEND_CLIENT_ID'],
    client_secret=app.config['KEYCLOAK_FRONTEND_CLIENT_SECRET'],
    server_metadata_url=app.config['KEYCLOAK_SERVER_METADATA_URL'],
    client_kwargs=app.config['KEYCLOAK_CLIENT_KWARGS'],
)

# 注册蓝图
app.register_blueprint(auth_bp)
app.register_blueprint(modal_bp)
app.register_blueprint(airtightness_bp)
app.register_blueprint(sound_insulation_bp)
app.register_blueprint(sound_absorption_bp)
app.register_blueprint(sound_transmission_bp)
app.register_blueprint(wall_mounted_transmission_bp)
app.register_blueprint(material_porosity_flow_resistance_bp)

# ========== 页面路由 ==========
@app.route('/')
@login_required
def index():
    """主页 - 需要认证"""
    return render_template('index.html')

@app.route('/test-tabs')
@login_required
def test_tabs():
    """测试标签页功能"""
    return render_template('test_tabs.html')

@app.route('/test-airtightness')
@login_required
def test_airtightness():
    """测试气密性功能"""
    return render_template('test_airtightness.html')

@app.route('/test-url-sync')
@login_required
def test_url_sync():
    """测试URL同步功能"""
    return render_template('test_url_sync.html')

@app.route('/login')
def login():
    """登录"""
    if 'user' in session:
        return redirect(url_for('index'))

    redirect_uri = url_for('auth_callback', _external=True)
    return keycloak.authorize_redirect(redirect_uri)

@app.route('/auth/callback')
def auth_callback():
    """认证回调"""
    try:
        token = keycloak.authorize_access_token()
        user = token.get('userinfo')
        if user:
            session['user'] = user
            session['token'] = token
            session['user_info'] = {
                'id': user.get('sub'),
                'username': user.get('preferred_username'),
                'email': user.get('email'),
                'name': user.get('name'),
                'roles': user.get('realm_access', {}).get('roles', [])
            }
        return redirect(url_for('index'))
    except Exception as e:
        print(f"认证回调错误: {e}")
        return redirect(url_for('login'))

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# 创建上传目录
upload_folder = app.config.get('UPLOAD_FOLDER')
if upload_folder and not os.path.exists(upload_folder):
    os.makedirs(upload_folder, exist_ok=True)
    os.makedirs(os.path.join(upload_folder, 'modal_shapes'), exist_ok=True)
    os.makedirs(os.path.join(upload_folder, 'test_photos'), exist_ok=True)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
