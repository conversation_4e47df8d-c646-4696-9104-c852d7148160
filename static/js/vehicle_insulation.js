// 车型隔声量对比功能JavaScript

// 多选框组件
class VehicleInsulationMultiSelect {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.selectedVehicles = [];
        this.allVehicles = [];
        this.isOpen = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadVehicles();
    }

    setupEventListeners() {
        const inputContainer = this.container.querySelector('.multiselect-input-container');
        const searchInput = this.container.querySelector('.multiselect-search input');

        // 点击输入框切换下拉菜单
        inputContainer.addEventListener('click', () => {
            this.toggle();
        });

        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            this.filterOptions(e.target.value);
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        });
    }

    async loadVehicles() {
        try {
            const response = await request.get('/sound_insulation/api/vehicle_insulation/vehicles');
            this.allVehicles = response.data;
            this.renderOptions();
        } catch (error) {
            showMessage('加载车型列表失败: ' + error.message, 'error');
        }
    }

    renderOptions() {
        const optionsContainer = this.container.querySelector('.multiselect-options');
        optionsContainer.innerHTML = '';

        this.allVehicles.forEach(vehicle => {
            const option = document.createElement('div');
            option.className = 'multiselect-option';
            option.innerHTML = `
                <input type="checkbox" id="vehicle-${vehicle.id}" ${this.isSelected(vehicle.id) ? 'checked' : ''}>
                <label for="vehicle-${vehicle.id}">${vehicle.name}</label>
            `;

            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const checkbox = option.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
                this.toggleVehicle(vehicle);
            });

            optionsContainer.appendChild(option);
        });
    }

    filterOptions(searchTerm) {
        const options = this.container.querySelectorAll('.multiselect-option');
        options.forEach(option => {
            const label = option.querySelector('label').textContent.toLowerCase();
            const isVisible = label.includes(searchTerm.toLowerCase());
            option.style.display = isVisible ? 'flex' : 'none';
        });
    }

    toggleVehicle(vehicle) {
        const index = this.selectedVehicles.findIndex(v => v.id === vehicle.id);
        if (index > -1) {
            this.selectedVehicles.splice(index, 1);
        } else {
            this.selectedVehicles.push(vehicle);
        }

        this.updateDisplay();
        this.updateSelectedOptions();
        this.onSelectionChange();
    }

    isSelected(vehicleId) {
        return this.selectedVehicles.some(v => v.id === vehicleId);
    }

    updateDisplay() {
        const input = this.container.querySelector('.multiselect-input');
        const selectedItemsContainer = this.container.querySelector('.selected-items');

        if (this.selectedVehicles.length === 0) {
            input.value = '';
            input.placeholder = '点击选择车型...';
            selectedItemsContainer.innerHTML = '';
        } else {
            input.value = `已选择 ${this.selectedVehicles.length} 个车型`;
            input.placeholder = '';

            // 渲染选中的车型标签
            selectedItemsContainer.innerHTML = this.selectedVehicles.map(vehicle => `
                <span class="selected-item">
                    ${vehicle.name}
                    <span class="remove-btn" data-vehicle-id="${vehicle.id}">×</span>
                </span>
            `).join('');

            // 绑定删除事件
            selectedItemsContainer.querySelectorAll('.remove-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const vehicleId = parseInt(btn.dataset.vehicleId);
                    const vehicle = this.selectedVehicles.find(v => v.id === vehicleId);
                    if (vehicle) {
                        this.toggleVehicle(vehicle);
                    }
                });
            });
        }
    }

    updateSelectedOptions() {
        const options = this.container.querySelectorAll('.multiselect-option');
        options.forEach(option => {
            const checkbox = option.querySelector('input[type="checkbox"]');
            const vehicleId = parseInt(checkbox.id.replace('vehicle-', ''));
            const isSelected = this.isSelected(vehicleId);

            checkbox.checked = isSelected;
            option.classList.toggle('selected', isSelected);
        });
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        this.container.querySelector('.multiselect-container').classList.add('open');
        this.isOpen = true;

        // 清空搜索框
        const searchInput = this.container.querySelector('.multiselect-search input');
        searchInput.value = '';
        this.filterOptions('');

        // 聚焦搜索框
        setTimeout(() => searchInput.focus(), 100);
    }

    close() {
        this.container.querySelector('.multiselect-container').classList.remove('open');
        this.isOpen = false;
    }

    getSelectedVehicleIds() {
        return this.selectedVehicles.map(v => v.id);
    }

    onSelectionChange() {
        // 子类可以重写此方法
    }
}

// 车型隔声量对比管理器
class VehicleInsulationManager {
    constructor() {
        this.multiSelect = null;
        this.currentComparisonData = null;
        this.chart = null;
        this.init();
    }

    init() {
        // 初始化多选框
        this.multiSelect = new VehicleInsulationMultiSelect('vehicle-multiselect');
        this.multiSelect.onSelectionChange = () => {
            this.updateGenerateButton();
        };

        this.setupEventListeners();
    }

    setupEventListeners() {
        // 生成对比表按钮
        document.getElementById('generate-btn').addEventListener('click', () => {
            this.generateComparison();
        });

        // 导出数据按钮
        document.getElementById('export-btn').addEventListener('click', () => {
            this.exportData();
        });
    }

    updateGenerateButton() {
        const btn = document.getElementById('generate-btn');
        const hasSelection = this.multiSelect.getSelectedVehicleIds().length > 0;
        btn.disabled = !hasSelection;
    }
    
    async generateComparison() {
        const vehicleIds = this.multiSelect.getSelectedVehicleIds();
        if (vehicleIds.length === 0) {
            showMessage('请至少选择一个车型', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await request.post('/sound_insulation/api/vehicle_insulation/comparison', {
                vehicle_ids: vehicleIds
            });

            this.currentComparisonData = response.data;
            this.displayComparisonResults();
            showMessage('对比数据生成成功', 'success');
        } catch (error) {
            showMessage('生成对比数据失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    displayComparisonResults() {
        const data = this.currentComparisonData;
        
        // 显示结果区域，隐藏空状态
        document.getElementById('results-card').style.display = 'block';
        document.getElementById('empty-state').style.display = 'none';
        
        // 更新车型数量
        document.getElementById('vehicle-count').textContent = `${data.vehicle_info.length} 个车型`;
        
        // 生成数据表格
        this.generateTable(data);
        
        // 生成图表
        this.generateChart(data);
        
        // 生成测试信息表
        this.generateTestInfoTable(data);
    }
    
    generateTable(data) {
        const table = document.getElementById('comparison-table');
        const thead = table.querySelector('thead');
        const tbody = table.querySelector('tbody');
        
        // 生成表头
        let headerHtml = '<tr><th class="frequency-column">中心频率(Hz)</th>';
        data.vehicle_info.forEach(vehicle => {
            headerHtml += `<th>${vehicle.name}<br><small class="text-muted">(dB)</small></th>`;
        });
        headerHtml += '</tr>';
        thead.innerHTML = headerHtml;
        
        // 生成表格数据
        let bodyHtml = '';
        data.table_data.forEach(row => {
            bodyHtml += `<tr><td class="frequency-column">${row.frequency}</td>`;
            data.vehicle_info.forEach(vehicle => {
                const value = row[`vehicle_${vehicle.id}`];
                const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
                bodyHtml += `<td>${displayValue}</td>`;
            });
            bodyHtml += '</tr>';
        });
        tbody.innerHTML = bodyHtml;
    }
    
    generateChart(data) {
        const chartContainer = document.getElementById('chart-container');
        
        if (this.chart) {
            this.chart.dispose();
        }
        
        this.chart = echarts.init(chartContainer);
        
        const option = {
            title: {
                text: '车型隔声量对比图',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = `频率: ${params[0].axisValue}Hz<br/>`;
                    params.forEach(param => {
                        if (param.value !== null) {
                            result += `${param.seriesName}: ${param.value.toFixed(2)} dB<br/>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: data.chart_data.series.map(s => s.name),
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.chart_data.frequencies,
                name: '频率(Hz)',
                nameLocation: 'middle',
                nameGap: 30
            },
            yAxis: {
                type: 'value',
                name: '隔声量(dB)',
                nameLocation: 'middle',
                nameGap: 50
            },
            series: data.chart_data.series.map((seriesData, index) => ({
                name: seriesData.name,
                type: 'line',
                data: seriesData.data,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 2
                },
                itemStyle: {
                    color: this.getChartColor(index)
                }
            }))
        };
        
        this.chart.setOption(option);
        
        // 绑定点击事件
        this.chart.on('click', (params) => {
            if (params.componentType === 'series') {
                const vehicleId = data.chart_data.series[params.seriesIndex].vehicle_id;
                this.showTestImage(vehicleId);
            }
        });
        
        // 响应式调整
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });
    }
    
    getChartColor(index) {
        const colors = [
            '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
            '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#5470c6'
        ];
        return colors[index % colors.length];
    }
    
    generateTestInfoTable(data) {
        const tbody = document.querySelector('#test-info-table tbody');
        let html = '';
        
        data.vehicle_info.forEach(vehicle => {
            html += `
                <tr>
                    <td>${vehicle.name}</td>
                    <td>${vehicle.test_date || '-'}</td>
                    <td>${vehicle.test_engineer || '-'}</td>
                    <td>${vehicle.test_location || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="vehicleInsulationManager.showTestImage(${vehicle.id})">
                            <i class="fas fa-image me-1"></i>查看附图
                        </button>
                    </td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html;
    }
    
    async showTestImage(vehicleId) {
        try {
            const response = await request.get(`/sound_insulation/api/vehicle_insulation/test_image?vehicle_id=${vehicleId}`);
            const imageInfo = response.data;
            
            // 更新模态框内容
            document.getElementById('modal-vehicle-name').textContent = imageInfo.vehicle_name || '-';
            document.getElementById('modal-test-date').textContent = imageInfo.test_date || '-';
            document.getElementById('modal-test-engineer').textContent = imageInfo.test_engineer || '-';
            document.getElementById('modal-test-location').textContent = imageInfo.test_location || '-';
            document.querySelector('#modal-remarks p').textContent = imageInfo.remarks || '-';
            
            const testImage = document.getElementById('test-image');
            const noImage = document.getElementById('no-image');
            
            if (imageInfo.test_image_path) {
                testImage.src = `/static/uploads/${imageInfo.test_image_path}`;
                testImage.style.display = 'block';
                noImage.style.display = 'none';
            } else {
                testImage.style.display = 'none';
                noImage.style.display = 'block';
            }
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
            
        } catch (error) {
            showMessage('获取测试图片失败: ' + error.message, 'error');
        }
    }
    
    async exportData() {
        const vehicleIds = this.multiSelect.getSelectedVehicleIds();
        if (vehicleIds.length === 0) {
            showMessage('请先生成对比数据', 'warning');
            return;
        }

        try {
            const response = await fetch('/sound_insulation/api/vehicle_insulation/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vehicle_ids: vehicleIds
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'vehicle_insulation_comparison.csv';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showMessage('数据导出成功', 'success');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            showMessage('导出数据失败: ' + error.message, 'error');
        }
    }
    
    showLoading(show) {
        const loading = document.getElementById('loading-indicator');
        const results = document.getElementById('results-card');
        const empty = document.getElementById('empty-state');

        if (show) {
            loading.style.display = 'block';
            results.style.display = 'none';
            empty.style.display = 'none';
        } else {
            loading.style.display = 'none';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;

    if (currentPath.includes('/sound_insulation/vehicle_insulation')) {
        window.vehicleInsulationManager = new VehicleInsulationManager();
    }
});

// 为标签页环境提供初始化函数
window.initializeVehicleInsulation = function() {
    console.log('initializeVehicleInsulation 被调用');

    // 等待一段时间确保DOM完全加载
    setTimeout(() => {
        if (typeof VehicleInsulationManager !== 'undefined') {
            console.log('创建 VehicleInsulationManager 实例');
            window.vehicleInsulationManager = new VehicleInsulationManager();
        } else {
            console.error('VehicleInsulationManager 未定义');
        }
    }, 200);
};
