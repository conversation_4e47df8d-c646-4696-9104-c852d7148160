<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL同步功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .log { background: #f5f5f5; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .info { background: #d1ecf1; padding: 10px; border-radius: 3px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>URL同步功能测试</h1>
    
    <div class="info">
        <strong>测试说明：</strong>
        <ul>
            <li>点击下面的链接测试URL同步功能</li>
            <li>观察地址栏URL是否正确更新</li>
            <li>使用浏览器前进/后退按钮测试</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>测试链接</h3>
        <button class="btn" onclick="testURL('/')">首页</button>
        <button class="btn" onclick="testURL('/airtightness/comparison')">气密性对比</button>
        <button class="btn" onclick="testURL('/airtightness/images')">气密性图片</button>
        <button class="btn" onclick="testURL('/modal/search')">模态数据</button>
        <button class="btn" onclick="testURL('/sound_insulation/area_comparison')">区域隔声量</button>
    </div>

    <div class="test-section">
        <h3>当前状态</h3>
        <p><strong>当前URL:</strong> <span id="current-url">-</span></p>
        <p><strong>测试时间:</strong> <span id="test-time">-</span></p>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <div id="log" class="log">等待测试...</div>
        <button class="btn" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('test-time').textContent = new Date().toLocaleString();
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '日志已清空...\n';
        }

        function testURL(url) {
            log(`测试URL: ${url}`);
            
            // 使用pushState更新URL
            window.history.pushState({testUrl: url}, '', url);
            updateStatus();
            log(`URL已更新为: ${window.location.href}`);
        }

        // 监听popstate事件
        window.addEventListener('popstate', function(event) {
            log(`popstate事件触发: ${JSON.stringify(event.state)}`);
            updateStatus();
        });

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            log('页面加载完成');
        });
    </script>
</body>
</html>
