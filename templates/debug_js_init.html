{% extends "base_tabs.html" %}

{% block title %}JavaScript初始化调试 - NVH数据管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">JavaScript初始化调试</h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bug me-2"></i>JavaScript初始化测试
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>测试按钮</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="testTabOpen('/sound_insulation/area_comparison', '区域隔声量', 'fas fa-chart-line')">
                                测试区域隔声量标签页
                            </button>
                            <button class="btn btn-info" onclick="testTabOpen('/sound_absorption/coefficient_query', '吸音系数查询', 'fas fa-search')">
                                测试吸音系数查询标签页
                            </button>
                            <button class="btn btn-success" onclick="testTabOpen('/sound_transmission/transmission_loss_query', '隔声量查询', 'fas fa-shield-alt')">
                                测试隔声量查询标签页
                            </button>
                            <button class="btn btn-warning" onclick="testDirectURL('/sound_insulation/area_comparison')">
                                直接访问区域隔声量URL
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>当前状态</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>当前URL:</strong></td>
                                <td id="current-url">-</td>
                            </tr>
                            <tr>
                                <td><strong>活跃标签页:</strong></td>
                                <td id="active-tab">-</td>
                            </tr>
                            <tr>
                                <td><strong>标签页数量:</strong></td>
                                <td id="tab-count">-</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>实时日志</h6>
                    <div id="debug-log" class="border rounded p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 0.875rem;">
                        <div class="text-muted">等待操作...</div>
                    </div>
                    <button class="btn btn-outline-secondary btn-sm mt-2" onclick="clearLog()">
                        <i class="fas fa-trash me-1"></i>清空日志
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面信息更新函数
function updatePageInfo() {
    document.getElementById('current-url').textContent = window.location.href;
    
    if (window.tabManager) {
        document.getElementById('active-tab').textContent = window.tabManager.activeTabId || '-';
        document.getElementById('tab-count').textContent = window.tabManager.tabs.size || '-';
    }
}

// 日志记录函数
function logMessage(message, type = 'info') {
    const logContainer = document.getElementById('debug-log');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = {
        'info': 'text-info',
        'success': 'text-success',
        'warning': 'text-warning',
        'error': 'text-danger'
    }[type] || 'text-dark';
    
    const logEntry = document.createElement('div');
    logEntry.className = colorClass;
    logEntry.innerHTML = `[${timestamp}] ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// 清空日志
function clearLog() {
    document.getElementById('debug-log').innerHTML = '<div class="text-muted">日志已清空...</div>';
}

// 测试函数
function testTabOpen(url, title, icon) {
    logMessage(`测试打开标签页: ${title} (${url})`, 'info');
    if (window.tabManager) {
        window.tabManager.openTab(url, title, icon);
        
        // 延迟检查初始化状态
        setTimeout(() => {
            checkInitializationStatus(url);
        }, 1000);
    } else {
        logMessage('错误: tabManager 未初始化', 'error');
    }
}

function testDirectURL(url) {
    logMessage(`测试直接访问URL: ${url}`, 'warning');
    window.location.href = url;
}

function checkInitializationStatus(url) {
    if (url.includes('/sound_insulation/area_comparison')) {
        if (window.soundInsulationManager) {
            logMessage('✓ SoundInsulationManager 已初始化', 'success');
        } else {
            logMessage('✗ SoundInsulationManager 未初始化', 'error');
        }
    } else if (url.includes('/sound_absorption/coefficient_query')) {
        if (window.soundAbsorptionQuery) {
            logMessage('✓ SoundAbsorptionQuery 已初始化', 'success');
        } else {
            logMessage('✗ SoundAbsorptionQuery 未初始化', 'error');
        }
    } else if (url.includes('/sound_transmission/transmission_loss_query')) {
        if (window.soundTransmissionQuery) {
            logMessage('✓ SoundTransmissionQuery 已初始化', 'success');
        } else {
            logMessage('✗ SoundTransmissionQuery 未初始化', 'error');
        }
    }
}

// 监听标签页变化
function monitorTabChanges() {
    if (window.tabManager) {
        const originalSwitchTab = window.tabManager.switchTab.bind(window.tabManager);
        window.tabManager.switchTab = function(tabId) {
            logMessage(`切换标签页: ${tabId}`, 'info');
            const result = originalSwitchTab(tabId);
            updatePageInfo();
            return result;
        };
        
        const originalOpenTab = window.tabManager.openTab.bind(window.tabManager);
        window.tabManager.openTab = function(url, title, icon) {
            logMessage(`打开新标签页: ${title} (${url})`, 'success');
            const result = originalOpenTab(url, title, icon);
            updatePageInfo();
            return result;
        };
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updatePageInfo();
    logMessage('调试页面加载完成', 'success');
    
    // 定期更新页面信息
    setInterval(updatePageInfo, 1000);
    
    // 延迟监听标签页变化（等待tabManager初始化）
    setTimeout(monitorTabChanges, 1000);
    
    // 监听popstate事件
    window.addEventListener('popstate', function(event) {
        logMessage(`浏览器前进/后退: ${JSON.stringify(event.state)}`, 'warning');
        updatePageInfo();
    });
});
</script>
{% endblock %}
