<!-- 孔隙率流阻查询页面内容片段 -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">孔隙率流阻查询</h1>
</div>

<!-- 查询条件 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="part-multiselect" class="form-label">零件（可多选）</label>
                    <div class="multiselect-container" id="part-multiselect">
                        <div class="multiselect-input-container">
                            <input type="text" class="form-control multiselect-input" placeholder="点击选择零件..." readonly>
                            <i class="fas fa-chevron-down multiselect-arrow"></i>
                        </div>
                        <div class="multiselect-dropdown">
                            <div class="multiselect-search">
                                <input type="text" class="form-control form-control-sm" placeholder="搜索零件...">
                            </div>
                            <div class="multiselect-options">
                                <!-- 动态加载选项 -->
                            </div>
                        </div>
                    </div>
                    <div class="selected-items mt-2">
                        <!-- 已选择的零件标签 -->
                    </div>
                </div>
                <div class="col-md-4">
                    <label for="material-multiselect" class="form-label">材料（可多选）</label>
                    <div class="multiselect-container" id="material-multiselect">
                        <div class="multiselect-input-container">
                            <input type="text" class="form-control multiselect-input" placeholder="点击选择材料..." readonly>
                            <i class="fas fa-chevron-down multiselect-arrow"></i>
                        </div>
                        <div class="multiselect-dropdown">
                            <div class="multiselect-search">
                                <input type="text" class="form-control form-control-sm" placeholder="搜索材料...">
                            </div>
                            <div class="multiselect-options">
                                <!-- 动态加载选项 -->
                            </div>
                        </div>
                    </div>
                    <div class="selected-items mt-2">
                        <!-- 已选择的材料标签 -->
                    </div>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-primary" id="search-btn">
                        <i class="fas fa-search me-1"></i>查询
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" id="reset-btn">
                        <i class="fas fa-undo me-1"></i>重置
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>查询结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span id="result-count" class="badge bg-secondary">0 条记录</span>
            <button type="button" class="btn btn-outline-success btn-sm" id="export-csv-btn">
                <i class="fas fa-download me-1"></i>导出CSV
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="data-table">
                <thead class="table-dark">
                    <tr>
                        <th>零件名称</th>
                        <th>材料名称</th>
                        <th>厚度 (mm)</th>
                        <th>孔隙率 (%)</th>
                        <th>流阻 (Pa·s/m²)</th>
                        <th>测试日期</th>
                        <th>测试工程师</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态生成数据 -->
                </tbody>
            </table>
        </div>
        
        <!-- 统计信息 -->
        <div class="mt-4">
            <h6><i class="fas fa-chart-bar me-2"></i>统计信息</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">孔隙率分布</h6>
                            <div id="porosity-chart" style="width: 100%; height: 300px;"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">流阻分布</h6>
                            <div id="flow-resistance-chart" style="width: 100%; height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-filter fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择查询条件</h5>
        <p class="text-muted">选择零件或材料，点击"查询"按钮查看孔隙率流阻数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在查询数据...</p>
</div>

<!-- 引入必要的CSS和JS -->
<link href="/static/css/material_porosity_flow_resistance.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
// 确保在标签页环境中正确加载脚本
if (typeof MaterialPorosityFlowResistanceQueryManager === 'undefined') {
    // 动态加载 material_porosity_flow_resistance.js
    const script = document.createElement('script');
    script.src = '/static/js/material_porosity_flow_resistance.js';
    script.onload = function() {
        // 脚本加载完成后初始化
        if (typeof window.initializeMaterialPorosityFlowResistanceQuery === 'function') {
            window.initializeMaterialPorosityFlowResistanceQuery();
        }
    };
    document.head.appendChild(script);
} else {
    // 脚本已加载，直接初始化
    setTimeout(() => {
        if (typeof window.initializeMaterialPorosityFlowResistanceQuery === 'function') {
            window.initializeMaterialPorosityFlowResistanceQuery();
        }
    }, 100);
}
</script>
