{% extends "base_tabs.html" %}

{% block title %}URL同步测试 - NVH数据管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">URL同步功能测试</h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>URL同步测试
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>测试说明</h6>
                    <p class="mb-2">此页面用于测试标签页URL同步功能：</p>
                    <ul class="mb-0">
                        <li>点击侧边栏链接时，URL应该更新为对应的路由地址</li>
                        <li>直接在地址栏输入URL时，应该自动打开对应的标签页</li>
                        <li>使用浏览器前进/后退按钮时，标签页应该正确切换</li>
                    </ul>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>当前页面信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>当前URL:</strong></td>
                                <td id="current-url">-</td>
                            </tr>
                            <tr>
                                <td><strong>活跃标签页:</strong></td>
                                <td id="active-tab">-</td>
                            </tr>
                            <tr>
                                <td><strong>标签页数量:</strong></td>
                                <td id="tab-count">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>测试链接</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="testTabOpen('/airtightness/comparison', '泄漏量对比', 'fas fa-chart-bar')">
                                <i class="fas fa-chart-bar me-2"></i>打开气密性泄漏量对比
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="testTabOpen('/airtightness/images', '测试图片', 'fas fa-images')">
                                <i class="fas fa-images me-2"></i>打开气密性测试图片
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="testTabOpen('/modal/search', '模态数据', 'fas fa-wave-square')">
                                <i class="fas fa-wave-square me-2"></i>打开模态数据查询
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="testDirectURL('/airtightness/comparison')">
                                <i class="fas fa-link me-2"></i>直接访问气密性对比URL
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="testSwitchTab('home')">
                                <i class="fas fa-home me-2"></i>切换到首页标签
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>实时日志</h6>
                    <div id="test-log" class="border rounded p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 0.875rem;">
                        <div class="text-muted">等待操作...</div>
                    </div>
                    <button class="btn btn-outline-secondary btn-sm mt-2" onclick="clearLog()">
                        <i class="fas fa-trash me-1"></i>清空日志
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面信息更新函数
function updatePageInfo() {
    document.getElementById('current-url').textContent = window.location.href;

    if (window.tabManager) {
        document.getElementById('active-tab').textContent = window.tabManager.activeTabId || '-';
        document.getElementById('tab-count').textContent = window.tabManager.tabs.size || '-';
    }
}

// 测试函数
function testTabOpen(url, title, icon) {
    logMessage(`测试打开标签页: ${title} (${url})`, 'info');
    if (window.tabManager) {
        window.tabManager.openTab(url, title, icon);
    } else {
        logMessage('错误: tabManager 未初始化', 'error');
    }
}

function testSwitchTab(tabId) {
    logMessage(`测试切换标签页: ${tabId}`, 'info');
    if (window.tabManager) {
        window.tabManager.switchTab(tabId);
    } else {
        logMessage('错误: tabManager 未初始化', 'error');
    }
}

function testDirectURL(url) {
    logMessage(`测试直接访问URL: ${url}`, 'warning');
    window.location.href = url;
}

// 日志记录函数
function logMessage(message, type = 'info') {
    const logContainer = document.getElementById('test-log');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = {
        'info': 'text-info',
        'success': 'text-success',
        'warning': 'text-warning',
        'error': 'text-danger'
    }[type] || 'text-dark';
    
    const logEntry = document.createElement('div');
    logEntry.className = colorClass;
    logEntry.innerHTML = `[${timestamp}] ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// 清空日志
function clearLog() {
    document.getElementById('test-log').innerHTML = '<div class="text-muted">日志已清空...</div>';
}

// 监听URL变化
let lastUrl = window.location.href;
function checkUrlChange() {
    const currentUrl = window.location.href;
    if (currentUrl !== lastUrl) {
        logMessage(`URL已变化: ${lastUrl} → ${currentUrl}`, 'success');
        lastUrl = currentUrl;
        updatePageInfo();
    }
}

// 监听标签页变化
function monitorTabChanges() {
    if (window.tabManager) {
        const originalSwitchTab = window.tabManager.switchTab.bind(window.tabManager);
        window.tabManager.switchTab = function(tabId) {
            logMessage(`切换标签页: ${tabId}`, 'info');
            const result = originalSwitchTab(tabId);
            updatePageInfo();
            return result;
        };
        
        const originalOpenTab = window.tabManager.openTab.bind(window.tabManager);
        window.tabManager.openTab = function(url, title, icon) {
            logMessage(`打开新标签页: ${title} (${url})`, 'success');
            const result = originalOpenTab(url, title, icon);
            updatePageInfo();
            return result;
        };
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updatePageInfo();
    logMessage('页面加载完成', 'success');
    
    // 定期检查URL变化
    setInterval(checkUrlChange, 500);
    
    // 延迟监听标签页变化（等待tabManager初始化）
    setTimeout(monitorTabChanges, 1000);
    
    // 监听popstate事件
    window.addEventListener('popstate', function(event) {
        logMessage(`浏览器前进/后退: ${JSON.stringify(event.state)}`, 'warning');
        updatePageInfo();
    });
    
    // 监听点击事件
    document.addEventListener('click', function(event) {
        if (event.target.tagName === 'A' && event.target.href) {
            const href = event.target.getAttribute('href');
            if (href && href.startsWith('/')) {
                logMessage(`点击链接: ${href}`, 'info');
            }
        }
    });
});
</script>
{% endblock %}
